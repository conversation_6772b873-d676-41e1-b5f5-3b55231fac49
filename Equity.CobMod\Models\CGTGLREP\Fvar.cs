using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtglrepDTO
{// <Section> Class for Fvar
public class Fvar
{
public Fvar() {}

// Fields in the class


// [DEBUG] Field: D95Record, is_external=, is_static_class=False, static_prefix=
private D95Record _D95Record = new D95Record();




// Getter and Setter methods

// Standard Getter
public D95Record GetD95Record()
{
    return _D95Record;
}

// Standard Setter
public void SetD95Record(D95Record value)
{
    _D95Record = value;
}

// Get<>AsString()
public string GetD95RecordAsString()
{
    return _D95Record != null ? _D95Record.GetD95RecordAsString() : "";
}

// Set<>AsString()
public void SetD95RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D95Record == null)
    {
        _D95Record = new D95Record();
    }
    _D95Record.SetD95RecordAsString(value);
}


}}